2025-07-10 08:57:08,340 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 08:57:08,340 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 08:57:08,340 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 08:57:08,340 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 08:57:08,340 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 08:57:08,340 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 08:57:09,226 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 08:57:09,229 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 08:57:09,229 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 08:57:37,284 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 08:57:37,284 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 08:57:37,284 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 08:57:37,284 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 08:57:37,284 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 08:57:37,284 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 08:57:38,013 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 08:57:38,017 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 08:57:38,017 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:03:12,025 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:03:12,026 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:03:12,026 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:03:12,026 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:03:12,026 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:03:12,026 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:03:12,757 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:03:12,760 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:03:12,760 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:08:10,786 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:08:10,787 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:08:10,787 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:08:10,787 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:08:10,787 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:08:10,787 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:08:12,801 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:08:12,804 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:08:12,805 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:09:48,910 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:09:48,910 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:09:48,910 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:09:48,910 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:09:48,910 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:09:48,910 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:09:49,564 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:09:49,566 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:09:49,566 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:11:58,433 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:11:58,433 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:11:58,433 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:11:58,433 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:11:58,433 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:11:58,433 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:11:59,053 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:11:59,055 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:11:59,056 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:32:29,043 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:32:29,054 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:32:29,055 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:32:29,055 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:32:29,055 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:32:29,055 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:32:29,055 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:32:30,407 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:32:30,407 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:32:30,407 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:35:29,515 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:35:29,520 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:35:29,520 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:35:29,520 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:35:29,520 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:35:29,521 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:35:29,521 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:35:30,051 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:35:30,052 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:35:30,052 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:37:15,128 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:37:15,135 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:37:15,135 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:37:15,135 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:37:15,136 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:37:15,136 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:37:15,136 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:37:15,974 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:37:15,977 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:37:15,977 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:39:30,403 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:39:30,408 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:39:30,409 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:39:30,409 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:39:30,409 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:39:30,409 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:39:30,409 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:39:31,263 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:39:31,265 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:39:31,266 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 09:53:26,826 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:53:26,829 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 09:53:26,830 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 09:53:26,830 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 09:53:26,830 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 09:53:26,830 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 09:53:26,830 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 09:53:27,933 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 09:53:27,936 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 09:53:27,936 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 12:38:47,184 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 12:38:47,189 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 12:38:47,189 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 12:38:47,189 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 12:38:47,189 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 12:38:47,189 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 12:38:47,189 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 12:38:47,767 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 12:38:47,769 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 12:38:47,769 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 12:53:59,474 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 12:53:59,478 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 12:53:59,479 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 12:53:59,479 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 12:53:59,479 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 12:53:59,479 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 12:53:59,479 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 12:54:00,051 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 12:54:00,054 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 12:54:00,054 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 12:59:27,720 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 12:59:27,730 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 12:59:27,730 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 12:59:27,730 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 12:59:27,730 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 12:59:27,730 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 12:59:27,730 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 12:59:28,226 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 12:59:28,228 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 12:59:28,229 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-10 13:00:48,688 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 13:00:48,690 - procesar_todo - INFO - Procesando datos para cédula: 39449326
2025-07-10 13:00:48,690 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-10 13:00:48,690 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-10 13:00:48,690 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-10 13:00:48,690 - procesar_todo - INFO - Continuando con el proceso...
2025-07-10 13:00:48,690 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-10 13:00:49,123 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-10 13:00:49,126 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-10 13:00:49,126 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 39449326
2025-07-11 15:04:24,295 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-11 15:04:24,299 - procesar_todo - INFO - Procesando datos para cédula: 15371254
2025-07-11 15:04:24,300 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-11 15:04:24,300 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-11 15:04:24,300 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-11 15:04:24,300 - procesar_todo - INFO - Continuando con el proceso...
2025-07-11 15:04:24,300 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-11 15:04:24,650 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-11 15:04:24,651 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-11 15:04:24,652 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 15371254
2025-07-11 15:04:58,823 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-11 15:04:58,826 - procesar_todo - INFO - Procesando datos para cédula: 15371254
2025-07-11 15:04:58,826 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-11 15:04:58,826 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-11 15:04:58,827 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-11 15:04:58,827 - procesar_todo - INFO - Continuando con el proceso...
2025-07-11 15:04:58,827 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-11 15:04:59,264 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-11 15:04:59,265 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-11 15:04:59,265 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 15371254
2025-07-11 15:07:17,344 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-11 15:07:17,348 - procesar_todo - INFO - Procesando datos para cédula: 15371254
2025-07-11 15:07:17,349 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-11 15:07:17,349 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-11 15:07:17,349 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-11 15:07:17,349 - procesar_todo - INFO - Continuando con el proceso...
2025-07-11 15:07:17,349 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-11 15:07:17,686 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-11 15:07:17,687 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-11 15:07:17,687 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 15371254
2025-07-11 15:10:22,247 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-11 15:10:22,250 - procesar_todo - INFO - Procesando datos para cédula: 1152441208
2025-07-11 15:10:22,251 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-11 15:10:22,251 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-11 15:10:22,251 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-11 15:10:22,251 - procesar_todo - INFO - Continuando con el proceso...
2025-07-11 15:10:22,251 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-11 15:10:22,586 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-11 15:10:22,588 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-11 15:10:22,588 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 1152441208
2025-07-11 15:13:47,924 - procesar_todo - INFO - Procesando datos para cédula: 15371254
2025-07-11 15:13:47,924 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-11 15:13:47,924 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-11 15:13:47,924 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-11 15:13:47,924 - procesar_todo - INFO - Continuando con el proceso...
2025-07-11 15:13:47,924 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-11 15:13:49,281 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-11 15:13:49,283 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-11 15:13:49,283 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 15371254
2025-07-11 15:14:44,013 - procesar_todo - INFO - Procesando datos para cédula: 1152441208
2025-07-11 15:14:44,014 - procesar_todo - INFO - Se encontraron 0 tickets en el reporte.
2025-07-11 15:14:44,014 - procesar_todo - WARNING - El reporte no contiene tickets. Puede que el correo llegue vacío.
2025-07-11 15:14:44,014 - procesar_todo - ERROR - Error al mostrar en consola: [Errno 2] No such file or directory: '/Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/open.py'
2025-07-11 15:14:44,014 - procesar_todo - INFO - Continuando con el proceso...
2025-07-11 15:14:44,014 - procesar_todo - INFO - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-07-11 15:14:44,339 - root - INFO - Correo enviado <NAME_EMAIL>
2025-07-11 15:14:44,341 - procesar_todo - INFO - Reporte enviado <NAME_EMAIL>
2025-07-11 15:14:44,341 - procesar_todo - INFO - Proceso completado exitosamente para la cédula 1152441208
2025-07-11 15:15:43,114 - procesar_todo - INFO - Procesando datos para cédula: 15371254
2025-07-11 15:15:43,114 - procesar_todo - ERROR - El archivo de reporte no existe: /Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/src/core/reporte_unificado.json
2025-07-11 15:15:43,115 - procesar_todo - ERROR - El proceso terminó con errores.
2025-07-11 15:17:05,495 - procesar_todo - INFO - Procesando datos para cédula: 1152441208
2025-07-11 15:17:05,495 - procesar_todo - ERROR - El archivo de reporte no existe: /Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/src/core/reporte_unificado.json
2025-07-11 15:17:05,495 - procesar_todo - ERROR - El proceso terminó con errores.
2025-07-11 15:18:56,121 - procesar_todo - INFO - Procesando datos para cédula: 15371254
2025-07-11 15:18:56,121 - procesar_todo - ERROR - El archivo de reporte no existe: /Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/src/core/reporte_unificado.json
2025-07-11 15:18:56,121 - procesar_todo - ERROR - El proceso terminó con errores.
2025-07-11 15:19:32,042 - procesar_todo - INFO - Procesando datos para cédula: 1152441208
2025-07-11 15:19:32,042 - procesar_todo - ERROR - El archivo de reporte no existe: /Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/src/core/reporte_unificado.json
2025-07-11 15:19:32,042 - procesar_todo - ERROR - El proceso terminó con errores.
2025-07-11 15:20:25,719 - procesar_todo - INFO - Procesando datos para cédula: 15371254
2025-07-11 15:20:25,719 - procesar_todo - ERROR - El archivo de reporte no existe: /Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/src/core/reporte_unificado.json
2025-07-11 15:20:25,719 - procesar_todo - ERROR - El proceso terminó con errores.
2025-07-11 15:24:32,963 - procesar_todo - INFO - Procesando datos para cédula: 1152441208
2025-07-11 15:24:32,963 - procesar_todo - ERROR - El archivo de reporte no existe: /Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/src/core/reporte_unificado.json
2025-07-11 15:24:32,963 - procesar_todo - ERROR - El proceso terminó con errores.
2025-07-11 15:25:24,892 - procesar_todo - INFO - Procesando datos para cédula: 15371254
2025-07-11 15:25:24,893 - procesar_todo - ERROR - El archivo de reporte no existe: /Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/src/core/reporte_unificado.json
2025-07-11 15:25:24,893 - procesar_todo - ERROR - El proceso terminó con errores.
2025-07-11 15:26:50,332 - procesar_todo - INFO - Procesando datos para cédula: 15371254
2025-07-11 15:26:50,333 - procesar_todo - ERROR - El archivo de reporte no existe: /Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/src/core/reporte_unificado.json
2025-07-11 15:26:50,333 - procesar_todo - ERROR - El proceso terminó con errores.
2025-07-11 15:27:57,205 - procesar_todo - INFO - Procesando datos para cédula: 15371254
2025-07-11 15:27:57,206 - procesar_todo - ERROR - El archivo de reporte no existe: /Users/<USER>/Downloads/bmc-remedy-container/bmc-remedy-organized/src/core/reporte_unificado.json
2025-07-11 15:27:57,206 - procesar_todo - ERROR - El proceso terminó con errores.
