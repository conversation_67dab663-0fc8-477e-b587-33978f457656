import json
import os
import sys
import datetime
import requests
from prettytable import PrettyTable
from colorama import init, Fore, Style
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path

# Agregar el directorio raíz al path para importaciones
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.core.a import TicketManager
from src.config.config import get_config, is_silent_mode
from src.config.logging_config import get_logger, log_operation_start, log_operation_success, log_operation_error, log_api_call

# Inicializar colorama para colores en la consola
init()

from src.utils.display import create_displayer

def mostrar_reporte_unificado(json_file: str, mostrar_todos: bool = False, limite: Optional[int] = None):
    """
    Muestra el reporte unificado con tablas en la consola.

    Args:
        json_file: Path to the JSON report file
        mostrar_todos: Whether to show all incidents or limit to default
        limite: Maximum number of incidents to show (overrides default limit)
    """
    logger = get_logger('open.mostrar_reporte')
    displayer = create_displayer()

    try:
        log_operation_start(logger, "mostrar reporte", archivo=json_file, mostrar_todos=mostrar_todos)

        # Verificar si el archivo existe
        if not os.path.exists(json_file):
            displayer.display_error(f"El archivo {json_file} no existe.")
            return

        # Cargar el archivo JSON
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Mostrar información de tickets
        _display_tickets_section(data, displayer, logger)

        # Mostrar información de incidentes
        _display_incidents_section(data, displayer, logger, mostrar_todos)

        log_operation_success(logger, "mostrar reporte", archivo=json_file)

    except json.JSONDecodeError as e:
        log_operation_error(logger, "mostrar reporte", e, archivo=json_file, error_type="JSON inválido")
        displayer.display_error("El archivo no es un JSON válido.")
    except Exception as e:
        log_operation_error(logger, "mostrar reporte", e, archivo=json_file)
        displayer.display_error(str(e))

def probar_busqueda_general(ticket_manager, logger):
    """
    Hace una búsqueda general para verificar que hay datos en el sistema
    """
    logger.info("Probando búsqueda general para verificar datos...")

    interfaces = [
        ("WOI:WorkOrderInterface", "Work Orders"),
        ("HPD:IncidentInterface", "Incidentes")
    ]

    for interface, nombre in interfaces:
        try:
            url = f"{ticket_manager.api_base_url}/arsys/v1/entry/{interface}"
            params = {"limit": "5"}  # Solo obtener 5 registros para verificar

            logger.info(f"Consultando {nombre} sin filtros...")
            response = requests.get(url, headers=ticket_manager.headers, params=params)

            if response.status_code == 200:
                data = response.json()
                entries = data.get('entries', [])
                logger.info(f"Encontrados {len(entries)} registros en {nombre}")

                if entries:
                    # Mostrar algunos campos de ejemplo
                    for i, entry in enumerate(entries[:2]):  # Solo los primeros 2
                        values = entry.get('values', {})
                        logger.info(f"Ejemplo {i+1} en {nombre}:")

                        # Mostrar campos relevantes para identificación
                        campos_relevantes = []
                        if interface == "WOI:WorkOrderInterface":
                            campos_relevantes = ['Work Order ID', 'Customer Person ID', 'Person ID', 'Requestor ID', 'Customer First Name', 'Customer Last Name', 'Status']
                        else:
                            campos_relevantes = ['Incident Number', 'Direct Contact Corporate ID', 'Direct Contact First Name', 'Direct Contact Last Name', 'Status']

                        for campo in campos_relevantes:
                            if campo in values and values[campo]:
                                logger.info(f"  {campo}: {values[campo]}")
                else:
                    logger.info(f"No hay registros en {nombre}")
            else:
                logger.error(f"Error consultando {nombre}: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"Error en búsqueda general de {nombre}: {str(e)}")

def explorar_campos_disponibles(ticket_manager, logger):
    """
    Explora los campos disponibles en las interfaces de BMC Remedy
    """
    logger.info("Explorando campos disponibles...")

    # Probar consulta simple para ver estructura de datos
    interfaces = [
        ("WOI:WorkOrderInterface", "Work Orders"),
        ("HPD:IncidentInterface", "Incidentes")
    ]

    for interface, nombre in interfaces:
        try:
            url = f"{ticket_manager.api_base_url}/arsys/v1/entry/{interface}"
            params = {"limit": "1"}  # Solo obtener 1 registro para ver estructura

            logger.info(f"Consultando {nombre} ({interface})...")
            response = requests.get(url, headers=ticket_manager.headers, params=params)

            if response.status_code == 200:
                data = response.json()
                entries = data.get('entries', [])
                if entries:
                    fields = list(entries[0].get('values', {}).keys())
                    logger.info(f"Campos disponibles en {nombre}: {fields}")

                    # Buscar campos relacionados con cédula/ID
                    cedula_fields = [f for f in fields if any(keyword in f.lower() for keyword in ['id', 'corporate', 'contact', 'customer', 'requestor', 'cedula'])]
                    logger.info(f"Campos posibles para cédula en {nombre}: {cedula_fields}")
                else:
                    logger.info(f"No hay registros en {nombre}")
            else:
                logger.error(f"Error consultando {nombre}: {response.status_code} - {response.text}")

        except Exception as e:
            logger.error(f"Error explorando {nombre}: {str(e)}")

def buscar_usuario_por_cedula(ticket_manager, cedula: str, logger):
    """
    Busca un usuario por cédula en CTM:People para obtener su información
    """
    logger.info(f"Buscando usuario con cédula {cedula} en CTM:People...")

    try:
        # URL para consultar usuario por cédula
        url = f"{ticket_manager.api_base_url}/arsys/v1/entry/CTM:People"
        params = {
            "q": f"'Corporate ID'=\"{cedula}\"",
            "limit": "1"
        }

        logger.info(f"URL usuario: {url}")
        logger.info(f"Parámetros usuario: {params}")

        response = requests.get(url, headers=ticket_manager.headers, params=params)
        logger.info(f"Status code usuario: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info(f"Respuesta usuario: {json.dumps(data, indent=2)}")
            entries = data.get('entries', [])

            if entries:
                user_data = entries[0].get('values', {})
                first_name = user_data.get('First Name', '')
                last_name = user_data.get('Last Name', '')
                full_name = f"{first_name} {last_name}".strip()

                logger.info(f"Usuario encontrado: {full_name}")
                logger.info(f"Datos del usuario: First Name='{first_name}', Last Name='{last_name}'")

                return {
                    'found': True,
                    'first_name': first_name,
                    'last_name': last_name,
                    'full_name': full_name,
                    'cedula': cedula,  # Incluir la cédula consultada
                    'user_data': user_data
                }
            else:
                logger.info(f"No se encontró usuario con cédula {cedula}")
                return {'found': False}
        else:
            logger.error(f"Error consultando usuario: {response.status_code} - {response.text}")
            return {'found': False}

    except Exception as e:
        logger.error(f"Error buscando usuario: {str(e)}")
        return {'found': False}

def buscar_tickets_por_nombre(ticket_manager, first_name: str, last_name: str, logger, user_info=None):
    """
    Busca tickets e incidentes usando el nombre y apellido del usuario
    """
    logger.info(f"Buscando tickets por nombre: '{first_name}' '{last_name}'")

    tickets = []
    incidentes = []

    try:
        # Buscar Work Orders por nombre
        api_base_url = ticket_manager.api_base_url
        url_work_orders = f"{api_base_url}/arsys/v1/entry/WOI:WorkOrderInterface"

        # Construir query para Work Orders con nombres
        query_parts_wo = []
        if first_name:
            query_parts_wo.append(f"'Customer First Name'=\"{first_name}\"")
        if last_name:
            query_parts_wo.append(f"'Customer Last Name'=\"{last_name}\"")
        if first_name and last_name:
            query_parts_wo.append(f"'CustomerFullName' LIKE \"%{first_name}%{last_name}%\"")
            query_parts_wo.append(f"'CustomerFullName' LIKE \"%{last_name}%{first_name}%\"")

        if query_parts_wo:
            query_wo = " OR ".join(query_parts_wo)
            params_wo = {
                "q": f"({query_wo})",
                "limit": "1000"
            }

            logger.info(f"Consultando Work Orders por nombre: {params_wo}")
            response_wo = requests.get(url_work_orders, headers=ticket_manager.headers, params=params_wo)

            if response_wo.status_code == 200:
                data_wo = response_wo.json()
                entries_wo = data_wo.get('entries', [])
                logger.info(f"Encontrados {len(entries_wo)} work orders por nombre")

                for entry in entries_wo:
                    values = entry.get('values', {})

                    # FILTRO: Verificar que la cédula coincida con la consultada
                    cedula_ticket = values.get('Customer Person ID', '') or values.get('Person ID', '') or values.get('Requestor ID', '')
                    if user_info and user_info.get('cedula') and cedula_ticket != user_info.get('cedula'):
                        continue  # Saltar este ticket si la cédula no coincide

                    # Procesar ticket igual que antes
                    summary = values.get('Summary') or values.get('Short Description') or 'Sin resumen disponible'
                    description = values.get('Detailed Description') or values.get('Description') or values.get('Notes') or 'Sin descripción disponible'

                    # Fix string concatenation with None values
                    customer_first = values.get('Customer First Name') or ''
                    customer_last = values.get('Customer Last Name') or ''
                    customer_full_name = f"{customer_first} {customer_last}".strip()
                    customer = values.get('Customer') or customer_full_name or values.get('Requestor') or 'No especificado'

                    # Intentar obtener fecha de creación de múltiples campos
                    fecha_creacion = (values.get('Create Date') or
                                    values.get('Submit Date') or
                                    values.get('Date/Time Stamp') or
                                    values.get('Entry Date'))

                    # Intentar extraer nombre del cliente de múltiples fuentes
                    nombre_cliente = customer.strip()
                    if not nombre_cliente or nombre_cliente == 'No especificado':
                        # Buscar en detailed_description
                        if 'Nombre completo del afectado:' in description:
                            import re
                            match = re.search(r'Nombre completo del afectado:\s*([^\n]+)', description)
                            if match:
                                nombre_cliente = match.group(1).strip()
                        # Alternativamente, usar otros campos
                        if not nombre_cliente or nombre_cliente == 'No especificado':
                            first_name = values.get('Direct Contact First Name') or ''
                            last_name = values.get('Direct Contact Last Name') or ''
                            nombre_cliente = f"{first_name} {last_name}".strip()
                        if not nombre_cliente:
                            nombre_cliente = values.get('Submitter', '').strip()

                    ticket_info = {
                        'request_id': values.get('Work Order ID'),
                        # 'summary': summary.strip(),  # ELIMINADO: Campo resumen removido completamente
                        'status': values.get('Status'),
                        'detailed_description': description.strip(),
                        'assigned_to': values.get('Assigned To'),
                        'assignee_groups': values.get('Assignee Groups'),
                        'last_modified_date': values.get('Last Modified Date'),
                        'create_date': fecha_creacion,
                        'priority': values.get('Priority'),
                        'impact': values.get('Impact'),
                        'urgency': values.get('Urgency'),
                        'submitter': values.get('Submitter'),
                        'customer': nombre_cliente,
                        'requestor_id': values.get('Requestor ID'),
                        'region': values.get('Region'),
                        'site_group': values.get('Site Group'),
                        'dwp_number': values.get('DWP_SRID') or values.get('Request ID') or 'N/A'
                    }
                    tickets.append(ticket_info)
            else:
                logger.error(f"Error consultando work orders por nombre: {response_wo.status_code} - {response_wo.text}")

        # Buscar Incidentes por nombre
        url_incidents = f"{api_base_url}/arsys/v1/entry/HPD:IncidentInterface"

        # Construir query para Incidentes con nombres
        query_parts_inc = []
        if first_name:
            query_parts_inc.append(f"'Direct Contact First Name'=\"{first_name}\"")
        if last_name:
            query_parts_inc.append(f"'Direct Contact Last Name'=\"{last_name}\"")

        # También buscar en Assignee Groups usando el login ID del usuario
        user_login = user_info.get('user_data', {}).get('Remedy Login ID', '') if user_info and user_info['found'] else ''
        if user_login:
            query_parts_inc.append(f"'Assignee Groups' LIKE \"%{user_login}%\"")
            logger.info(f"Agregando búsqueda por Assignee Groups: {user_login}")

        if query_parts_inc:
            query_inc = " OR ".join(query_parts_inc)
            params_inc = {
                "q": f"({query_inc})",
                "limit": "1000"
            }

            logger.info(f"Consultando Incidentes por nombre: {params_inc}")
            response_inc = requests.get(url_incidents, headers=ticket_manager.headers, params=params_inc)

            if response_inc.status_code == 200:
                data_inc = response_inc.json()
                entries_inc = data_inc.get('entries', [])
                logger.info(f"Encontrados {len(entries_inc)} incidentes por nombre")

                for entry in entries_inc:
                    values = entry.get('values', {})

                    # FILTRO: Verificar que la cédula coincida con la consultada
                    cedula_incidente = values.get('Direct Contact Corporate ID', '') or values.get('Corporate ID', '') or values.get('Customer Corporate ID', '')
                    if user_info and user_info.get('cedula') and cedula_incidente != user_info.get('cedula'):
                        continue  # Saltar este incidente si la cédula no coincide

                    # CORRECCIÓN FINAL: Description va en resumen, Detailed Decription va en descripción detallada
                    summary = values.get('Description') or values.get('Short Description') or 'Sin resumen disponible'
                    # El campo detallado debe ser "Detailed Decription" (información completa)
                    description = values.get('Detailed Decription') or values.get('Detailed Description') or values.get('Notes') or 'Sin descripción disponible'
                    customer = values.get('Customer') or values.get('Customer First Name', '') + ' ' + values.get('Customer Last Name', '') or values.get('Requestor') or 'No especificado'

                    # Extraer AMBOS campos por separado para mostrarlos como campos independientes
                    description_field = values.get('Description', '')  # Campo corto para resumen
                    detailed_decription_field = values.get('Detailed Decription', '') or values.get('Detailed Description', '')  # Campo largo para descripción detallada

                    # CAMBIO: Description como campo principal (resumen)
                    descripcion_principal = description_field if description_field else 'Sin descripción disponible'

                    # Detailed Description como campo secundario
                    descripcion_detallada = detailed_decription_field if detailed_decription_field else 'Sin descripción detallada disponible'

                    # Buscar campos relacionados con aplicativos y equipos (información adicional)
                    aplicativo_info = []
                    for key, value in values.items():
                        if value and isinstance(value, str):
                            key_lower = key.lower()
                            if any(term in key_lower for term in ['aplicativo', 'application', 'app', 'dwp', 'equipo', 'equipment', 'device', 'ci', 'service', 'categorization', 'tier']):
                                aplicativo_info.append(f"{key}: {value}")

                    # Crear información técnica separada
                    info_tecnica = "\\n".join(aplicativo_info) if aplicativo_info else 'Sin información técnica disponible'

                    # Intentar obtener fecha de creación de múltiples campos
                    fecha_creacion = (values.get('Create Date') or
                                    values.get('Submit Date') or
                                    values.get('Date/Time Stamp') or
                                    values.get('Entry Date'))

                    # Intentar extraer nombre del cliente de múltiples fuentes
                    nombre_cliente = customer.strip()
                    if not nombre_cliente:
                        # Buscar en detailed_description
                        if 'Nombre completo del afectado:' in descripcion_detallada:
                            import re
                            match = re.search(r'Nombre completo del afectado:\s*([^\n]+)', descripcion_detallada)
                            if match:
                                nombre_cliente = match.group(1).strip()
                        # Alternativamente, usar otros campos
                        if not nombre_cliente:
                            first_name = values.get('Direct Contact First Name') or ''
                            last_name = values.get('Direct Contact Last Name') or ''
                            nombre_cliente = f"{first_name} {last_name}".strip()
                        if not nombre_cliente:
                            nombre_cliente = values.get('Submitter', '').strip()

                    incidente_info = {
                        'incident_id': values.get('Incident Number'),
                        # 'summary': description.strip(),  # ELIMINADO: Campo resumen removido completamente
                        'status': values.get('Status'),
                        'description': descripcion_principal,  # Campo Description como principal
                        'detailed_description': descripcion_detallada,  # Campo Detailed Decription como secundario
                        'technical_info': info_tecnica,  # Información técnica separada
                        'assigned_to': values.get('Assigned To'),
                        'assignee_groups': values.get('Assignee Groups'),
                        'last_modified_date': values.get('Last Modified Date'),
                        'create_date': fecha_creacion,
                        'priority': values.get('Priority'),
                        'impact': values.get('Impact'),
                        'urgency': values.get('Urgency'),
                        'customer': nombre_cliente,
                        'login_id': values.get('Requestor ID'),
                        'service_type': values.get('Service Type'),
                        'service': values.get('Service'),
                        'region': values.get('Region'),
                        'site_group': values.get('Site Group'),
                        'resolution': values.get('Resolution'),
                        'resolution_method': values.get('Resolution Method'),
                        'dwp_number': values.get('DWP_SRID') or values.get('Incident Number') or 'N/A'
                    }
                    incidentes.append(incidente_info)
            else:
                logger.error(f"Error consultando incidentes por nombre: {response_inc.status_code} - {response_inc.text}")

        return tickets, incidentes

    except Exception as e:
        logger.error(f"Error buscando tickets por nombre: {str(e)}")
        return [], []

def extraer_info_completa_dwp(ticket_manager, dwp_number: str, logger):
    """
    Extrae TODA la información disponible de un caso DWP específico
    """
    logger.info(f"Extrayendo información completa del caso DWP: {dwp_number}")

    try:
        api_base_url = ticket_manager.api_base_url

        # Buscar Incidentes por DWP
        url_incidents = f"{api_base_url}/arsys/v1/entry/HPD:IncidentInterface"
        params_inc = {
            "q": f"'DWP_SRID'=\"{dwp_number}\"",
            "limit": "1000"
        }

        logger.info(f"Buscando información completa del incidente DWP {dwp_number}")
        response_inc = requests.get(url_incidents, headers=ticket_manager.headers, params=params_inc)

        if response_inc.status_code == 200:
            data_inc = response_inc.json()
            entries_inc = data_inc.get('entries', [])
            logger.info(f"Encontrados {len(entries_inc)} incidentes por DWP {dwp_number}")

            if entries_inc:
                # Tomar el primer incidente encontrado
                entry = entries_inc[0]
                values = entry.get('values', {})

                print(f"\n{'='*80}")
                print(f"INFORMACIÓN COMPLETA DEL CASO DWP {dwp_number}")
                print(f"{'='*80}")
                print(f"Incident Number: {values.get('Incident Number')}")
                print(f"{'='*80}")

                # Mostrar TODOS los campos disponibles ordenados alfabéticamente
                campos_ordenados = sorted(values.items())

                for campo, valor in campos_ordenados:
                    if valor is not None and str(valor).strip():
                        print(f"{campo}: {valor}")

                print(f"{'='*80}")
                print(f"TOTAL DE CAMPOS DISPONIBLES: {len([k for k, v in values.items() if v is not None and str(v).strip()])}")
                print(f"{'='*80}")

                return values
            else:
                logger.warning(f"No se encontró información para el DWP {dwp_number}")
                return None
        else:
            logger.error(f"Error buscando incidente por DWP: {response_inc.status_code} - {response_inc.text}")
            return None

    except Exception as e:
        logger.error(f"Error extrayendo información completa del DWP {dwp_number}: {str(e)}")
        return None

def buscar_por_dwp(ticket_manager, dwp_number: str, logger):
    """
    Busca tickets e incidentes por número DWP específico
    """
    logger.info(f"Buscando casos por número DWP: {dwp_number}")

    tickets = []
    incidentes = []

    try:
        api_base_url = ticket_manager.api_base_url

        # Buscar Work Orders por DWP
        url_work_orders = f"{api_base_url}/arsys/v1/entry/WOI:WorkOrderInterface"
        params_wo = {
            "q": f"'DWP_SRID'=\"{dwp_number}\"",
            "limit": "1000"
        }

        logger.info(f"Buscando Work Orders por DWP {dwp_number}")
        response_wo = requests.get(url_work_orders, headers=ticket_manager.headers, params=params_wo)

        if response_wo.status_code == 200:
            data_wo = response_wo.json()
            entries_wo = data_wo.get('entries', [])
            logger.info(f"Encontrados {len(entries_wo)} work orders por DWP {dwp_number}")

            for entry in entries_wo:
                values = entry.get('values', {})

                # FILTRO: Para búsquedas por DWP, verificar que el DWP coincida
                dwp_ticket = values.get('DWP_SRID', '') or values.get('Request ID', '')
                if dwp_ticket and dwp_ticket != dwp_number:
                    continue  # Saltar este ticket si el DWP no coincide exactamente

                summary = values.get('Summary') or values.get('Short Description') or 'Sin resumen disponible'
                description = values.get('Detailed Description') or values.get('Description') or values.get('Notes') or 'Sin descripción disponible'

                # Fix string concatenation with None values
                customer_first = values.get('Customer First Name') or ''
                customer_last = values.get('Customer Last Name') or ''
                customer_full_name = f"{customer_first} {customer_last}".strip()
                customer = values.get('Customer') or customer_full_name or values.get('Requestor') or 'No especificado'

                # Intentar obtener fecha de creación de múltiples campos
                fecha_creacion = (values.get('Create Date') or
                                values.get('Submit Date') or
                                values.get('Date/Time Stamp') or
                                values.get('Entry Date'))

                # Intentar extraer nombre del cliente de múltiples fuentes
                nombre_cliente = customer.strip()
                if not nombre_cliente or nombre_cliente == 'No especificado':
                    # Buscar en detailed_description
                    if 'Nombre completo del afectado:' in description:
                        import re
                        match = re.search(r'Nombre completo del afectado:\s*([^\n]+)', description)
                        if match:
                            nombre_cliente = match.group(1).strip()
                    # Alternativamente, usar otros campos
                    if not nombre_cliente or nombre_cliente == 'No especificado':
                        first_name = values.get('Direct Contact First Name') or ''
                        last_name = values.get('Direct Contact Last Name') or ''
                        nombre_cliente = f"{first_name} {last_name}".strip()
                    if not nombre_cliente:
                        nombre_cliente = values.get('Submitter', '').strip()

                ticket_info = {
                    'request_id': values.get('Work Order ID'),
                    # 'summary': summary.strip(),  # ELIMINADO: Campo resumen removido completamente
                    'status': values.get('Status'),
                    'detailed_description': description.strip(),
                    'assigned_to': values.get('Assigned To'),
                    'assignee_groups': values.get('Assignee Groups'),
                    'last_modified_date': values.get('Last Modified Date'),
                    'create_date': fecha_creacion,
                    'priority': values.get('Priority'),
                    'impact': values.get('Impact'),
                    'urgency': values.get('Urgency'),
                    'submitter': values.get('Submitter'),
                    'customer': nombre_cliente,
                    'requestor_id': values.get('Requestor ID'),
                    'region': values.get('Region'),
                    'site_group': values.get('Site Group'),
                    'dwp_number': values.get('DWP_SRID') or values.get('Request ID') or 'N/A'
                }
                tickets.append(ticket_info)
        else:
            logger.error(f"Error buscando work orders por DWP: {response_wo.status_code} - {response_wo.text}")

        # Buscar Incidentes por DWP
        url_incidents = f"{api_base_url}/arsys/v1/entry/HPD:IncidentInterface"
        params_inc = {
            "q": f"'DWP_SRID'=\"{dwp_number}\"",
            "limit": "1000"
        }

        logger.info(f"Buscando Incidentes por DWP {dwp_number}")
        response_inc = requests.get(url_incidents, headers=ticket_manager.headers, params=params_inc)

        if response_inc.status_code == 200:
            data_inc = response_inc.json()
            entries_inc = data_inc.get('entries', [])
            logger.info(f"Encontrados {len(entries_inc)} incidentes por DWP {dwp_number}")

            for entry in entries_inc:
                values = entry.get('values', {})

                # FILTRO: Para búsquedas por DWP, verificar que el DWP coincida
                dwp_incidente = values.get('DWP_SRID', '') or values.get('Incident Number', '')
                if dwp_incidente and dwp_incidente != dwp_number:
                    continue  # Saltar este incidente si el DWP no coincide exactamente

                # CORRECCIÓN FINAL: Description va en resumen, Detailed Decription va en descripción detallada
                description_field = values.get('Description', '').strip()  # Campo corto para resumen
                detailed_decription_field = values.get('Detailed Decription', '') or values.get('Detailed Description', '') or values.get('Notes', '')  # Campo largo para descripción detallada

                # LÓGICA MEJORADA: Si Description está vacío, usar un resumen genérico basado en el tipo de caso
                if description_field:
                    # Usar el campo Description como resumen
                    descripcion_principal = description_field  # CORRECCIÓN: usar directamente el campo corto
                else:
                    # Si Description está vacío, crear un resumen genérico más descriptivo
                    incident_id = values.get('Incident Number', 'N/A')
                    status = values.get('Status', 'N/A')

                    # Usar la descripción completa sin truncar
                    if detailed_decription_field:
                        descripcion_principal = detailed_decription_field
                    else:
                        descripcion_principal = f"Incidente {incident_id} - Estado: {status}"

                # La descripción detallada siempre usa el campo largo
                description = detailed_decription_field if detailed_decription_field else 'Sin descripción disponible'
                customer = values.get('Customer') or values.get('Customer First Name', '') + ' ' + values.get('Customer Last Name', '') or values.get('Requestor') or 'No especificado'

                # Detailed Description como campo secundario
                descripcion_detallada = detailed_decription_field if detailed_decription_field else 'Sin descripción detallada disponible'

                # Buscar campos relacionados con aplicativos y equipos (información adicional)
                aplicativo_info = []
                for key, value in values.items():
                    if value and isinstance(value, str):
                        key_lower = key.lower()
                        if any(term in key_lower for term in ['aplicativo', 'application', 'app', 'dwp', 'equipo', 'equipment', 'device', 'ci', 'service', 'categorization', 'tier']):
                            aplicativo_info.append(f"{key}: {value}")

                # Crear información técnica separada
                info_tecnica = "\\n".join(aplicativo_info) if aplicativo_info else 'Sin información técnica disponible'

                # Intentar obtener fecha de creación de múltiples campos
                fecha_creacion = (values.get('Create Date') or
                                values.get('Submit Date') or
                                values.get('Date/Time Stamp') or
                                values.get('Entry Date'))

                # Intentar extraer nombre del cliente de múltiples fuentes
                nombre_cliente = customer.strip()
                if not nombre_cliente:
                    # Buscar en detailed_description
                    if 'Nombre completo del afectado:' in descripcion_detallada:
                        import re
                        match = re.search(r'Nombre completo del afectado:\s*([^\n]+)', descripcion_detallada)
                        if match:
                            nombre_cliente = match.group(1).strip()
                    # Alternativamente, usar otros campos
                    if not nombre_cliente:
                        first_name = values.get('Direct Contact First Name') or ''
                        last_name = values.get('Direct Contact Last Name') or ''
                        nombre_cliente = f"{first_name} {last_name}".strip()
                    if not nombre_cliente:
                        nombre_cliente = values.get('Submitter', '').strip()

                incidente_info = {
                    'incident_id': values.get('Incident Number'),
                    # 'summary': descripcion_principal,  # ELIMINADO: Campo resumen removido completamente
                    'status': values.get('Status'),
                    'description': descripcion_principal,  # Campo Description como principal
                    'detailed_description': descripcion_detallada,  # Campo Detailed Decription como secundario
                    'technical_info': info_tecnica,  # Información técnica separada
                    'assigned_to': values.get('Assigned To'),
                    'assignee_groups': values.get('Assignee Groups'),
                    'last_modified_date': values.get('Last Modified Date'),
                    'create_date': fecha_creacion,
                    'priority': values.get('Priority'),
                    'impact': values.get('Impact'),
                    'urgency': values.get('Urgency'),
                    'customer': nombre_cliente,
                    'login_id': values.get('Requestor ID'),
                    'service_type': values.get('Service Type'),
                    'service': values.get('Service'),
                    'region': values.get('Region'),
                    'site_group': values.get('Site Group'),
                    'resolution': values.get('Resolution'),
                    'resolution_method': values.get('Resolution Method'),
                    'dwp_number': values.get('DWP_SRID') or values.get('Incident Number') or 'N/A'
                }
                incidentes.append(incidente_info)
        else:
            logger.error(f"Error buscando incidentes por DWP: {response_inc.status_code} - {response_inc.text}")

        return tickets, incidentes

    except Exception as e:
        logger.error(f"Error buscando por DWP {dwp_number}: {str(e)}")
        return [], []

def buscar_tickets_por_cedula(cedula: str, todos: bool = False) -> Optional[str]:
    """
    Busca tickets e incidentes para una cédula específica en BMC Remedy.

    Args:
        cedula: Número de cédula a consultar
        todos: Si es True, incluye todos los incidentes

    Returns:
        Path to the generated report file, or None if error
    """
    logger = get_logger('open.buscar_tickets')

    try:
        log_operation_start(logger, "búsqueda de tickets", cedula=cedula, mostrar_todos=todos)

        # Crear instancia del TicketManager
        ticket_manager = TicketManager()

        # Paso 1: Buscar usuario por cédula para obtener nombre
        user_info = buscar_usuario_por_cedula(ticket_manager, cedula, logger)

        tickets = []
        incidentes = []

        if user_info['found']:
            # Paso 2: Buscar tickets por nombre
            logger.info(f"Usuario encontrado, buscando tickets por nombre...")
            tickets_por_nombre, incidentes_por_nombre = buscar_tickets_por_nombre(
                ticket_manager,
                user_info['first_name'],
                user_info['last_name'],
                logger,
                user_info
            )
            tickets.extend(tickets_por_nombre)
            incidentes.extend(incidentes_por_nombre)

        # Paso 3: Buscar por DWP específico si se proporciona
        if cedula == "65883":  # Caso específico para probar
            logger.info(f"Búsqueda específica por DWP {cedula}")

            # Extraer información completa primero
            info_completa = extraer_info_completa_dwp(ticket_manager, cedula, logger)

            # Luego hacer la búsqueda normal
            tickets_dwp, incidentes_dwp = buscar_por_dwp(ticket_manager, cedula, logger)
            tickets.extend(tickets_dwp)
            incidentes.extend(incidentes_dwp)

        # Paso 4: También buscar por cédula (método original) como respaldo
        logger.info(f"Buscando también por cédula como método de respaldo...")

        # Consulta directa a la API para obtener tickets (Work Orders)
        logger.info(f"Buscando tickets (Work Orders) para cédula {cedula}")
        # URL para consultar work orders
        api_base_url = ticket_manager.api_base_url
        url_work_orders = f"{api_base_url}/arsys/v1/entry/WOI:WorkOrderInterface"
        
        # Parámetros de consulta para work orders - probar diferentes campos
        # El campo 'Direct Contact Corporate ID' no existe en Work Orders
        # Vamos a probar varios campos posibles
        campos_posibles_wo = [
            'Customer Person ID',
            'Person ID',
            'Requestor ID',
            'Customer Company',
            'Customer First Name',
            'Customer Last Name'
        ]

        # Construir query con múltiples campos usando OR
        query_parts = []
        for campo in campos_posibles_wo:
            query_parts.append(f"'{campo}'=\"{cedula}\"")

        query_wo = " OR ".join(query_parts)

        params_wo = {
            "q": f"({query_wo})",
            "limit": "1000"  # Aumentar límite para obtener más resultados
        }
        
        # Realizar la consulta para work orders por cédula
        logger.info(f"URL: {url_work_orders}")
        logger.info(f"Parámetros: {params_wo}")

        response_wo = requests.get(url_work_orders, headers=ticket_manager.headers, params=params_wo)

        logger.info(f"Status code work orders por cédula: {response_wo.status_code}")
        if response_wo.status_code != 200:
            logger.error(f"Error en work orders por cédula: {response_wo.text}")

        # Agregar tickets encontrados por cédula a la lista existente
        if response_wo.status_code == 200:
            data_wo = response_wo.json()
            entries_wo = data_wo.get('entries', [])
            
            for entry in entries_wo:
                values = entry.get('values', {})

                # FILTRO: Verificar que la cédula coincida con la consultada
                cedula_ticket = values.get('Customer Person ID', '') or values.get('Person ID', '') or values.get('Requestor ID', '')
                if cedula_ticket and cedula_ticket != cedula:
                    continue  # Saltar este ticket si la cédula no coincide

                # Mejorar el manejo de campos vacíos
                summary = values.get('Summary') or values.get('Short Description') or 'Sin resumen disponible'
                description = values.get('Detailed Description') or values.get('Description') or values.get('Notes') or 'Sin descripción disponible'
                customer = values.get('Customer') or values.get('Customer First Name', '') + ' ' + values.get('Customer Last Name', '') or values.get('Requestor') or 'No especificado'

                # Intentar obtener fecha de creación de múltiples campos
                fecha_creacion = (values.get('Create Date') or
                                values.get('Submit Date') or
                                values.get('Date/Time Stamp') or
                                values.get('Entry Date'))

                # Intentar extraer nombre del cliente de múltiples fuentes
                nombre_cliente = customer.strip()
                if not nombre_cliente or nombre_cliente == 'No especificado':
                    # Buscar en detailed_description
                    if 'Nombre completo del afectado:' in description:
                        import re
                        match = re.search(r'Nombre completo del afectado:\s*([^\n]+)', description)
                        if match:
                            nombre_cliente = match.group(1).strip()
                    # Alternativamente, usar otros campos
                    if not nombre_cliente or nombre_cliente == 'No especificado':
                        nombre_cliente = (values.get('Direct Contact First Name', '') + ' ' +
                                        values.get('Direct Contact Last Name', '')).strip()
                    if not nombre_cliente:
                        nombre_cliente = values.get('Submitter', '').strip()

                ticket_info = {
                    'request_id': values.get('Work Order ID'),
                    # 'summary': summary.strip(),  # ELIMINADO: Campo resumen removido completamente
                    'status': values.get('Status'),
                    'detailed_description': description.strip(),
                    'assigned_to': values.get('Assigned To'),
                    'assignee_groups': values.get('Assignee Groups'),
                    'last_modified_date': values.get('Last Modified Date'),
                    'create_date': fecha_creacion,
                    'priority': values.get('Priority'),
                    'impact': values.get('Impact'),
                    'urgency': values.get('Urgency'),
                    'submitter': values.get('Submitter'),
                    'customer': nombre_cliente,
                    'requestor_id': values.get('Requestor ID'),
                    'region': values.get('Region'),
                    'site_group': values.get('Site Group'),
                    'dwp_number': values.get('DWP_SRID') or values.get('Request ID') or 'N/A'
                }
                tickets.append(ticket_info)
        
        # Obtener notas para cada ticket
        for ticket in tickets:
            try:
                # Consultar notas del work order
                work_order_id = ticket.get('request_id')
                if work_order_id:
                    # URL para consultar notas de work orders
                    url_notes = f"{api_base_url}/arsys/v1/entry/WOI:WorkInfo"
                    params_notes = {
                        "q": f"'Work Order ID'=\"{work_order_id}\"",
                        "limit": "2"  # Las dos notas más recientes
                    }
                    
                    response_notes = requests.get(url_notes, headers=ticket_manager.headers, params=params_notes)
                    
                    if response_notes.status_code == 200:
                        notes_data = response_notes.json()
                        notes_entries = notes_data.get('entries', [])
                        
                        if notes_entries:
                            # Crear lista con las notas encontradas
                            notes_list = []
                            for i, entry in enumerate(notes_entries):
                                note_values = entry.get('values', {})
                                note_info = {
                                    'note_number': i + 1,
                                    'description': note_values.get('Summary', 'Sin resumen'),
                                    'detailed_description': note_values.get('Detailed Description', 'Sin descripción detallada'),
                                    'submitter': note_values.get('Submitter', 'N/A'),
                                    'last_modified_date': note_values.get('Last Modified Date', 'N/A')
                                }
                                notes_list.append(note_info)
                            
                            # Solo mantener last_note, eliminar notes
                            ticket['last_note'] = notes_list[0] if notes_list else None
                        else:
                            ticket['last_note'] = {
                                'description': 'Sin notas',
                                'detailed_description': 'No hay notas disponibles para este ticket'
                            }
                    else:
                        ticket['last_note'] = {
                            'description': 'Error al obtener notas',
                            'detailed_description': f'Error {response_notes.status_code}: {response_notes.text}'
                        }
                else:
                    ticket['last_note'] = {
                        'description': 'Sin ID de ticket',
                        'detailed_description': 'No se puede consultar notas sin ID de ticket'
                    }
            except Exception as e:
                ticket['last_note'] = {
                    'description': 'Error en consulta',
                    'detailed_description': f'Error al consultar notas: {str(e)}'
                }
        
        logger.info(f"Se encontraron {len(tickets)} work orders")

        # Consulta directa a la API para obtener incidentes
        logger.info(f"Buscando incidentes para cédula {cedula}")
        # URL para consultar incidentes
        url_incidents = f"{api_base_url}/arsys/v1/entry/HPD:IncidentInterface"

        # Parámetros de consulta para incidentes - incluir todos los estados
        params_inc = {
            "q": f"'Direct Contact Corporate ID'=\"{cedula}\"",
            "limit": "1000"  # Aumentar límite para obtener más resultados
        }

        # Realizar la consulta para incidentes por cédula
        logger.info(f"URL incidentes: {url_incidents}")
        logger.info(f"Parámetros incidentes: {params_inc}")

        response_inc = requests.get(url_incidents, headers=ticket_manager.headers, params=params_inc)

        logger.info(f"Status code incidentes por cédula: {response_inc.status_code}")
        if response_inc.status_code != 200:
            logger.error(f"Error en incidentes por cédula: {response_inc.text}")

        # NUEVA BÚSQUEDA: También buscar por Assignee Groups usando el Login ID del usuario
        if user_info['found'] and user_info.get('user_data', {}).get('Remedy Login ID'):
            login_id = user_info['user_data']['Remedy Login ID']
            logger.info(f"Buscando incidentes adicionales por Assignee Groups para login ID: {login_id}")

            # Usar HPD:Help Desk para búsqueda por Assignee Groups (donde están los casos que faltan)
            url_incidents_assignee = f"{api_base_url}/arsys/v1/entry/HPD:Help Desk"
            params_inc_assignee = {
                "q": f"'Assignee Groups' LIKE \"%'{login_id}'%\"",
                "limit": "1000"
            }

            logger.info(f"URL incidentes por Assignee Groups: {url_incidents_assignee}")
            logger.info(f"Parámetros incidentes por Assignee Groups: {params_inc_assignee}")

            response_inc_assignee = requests.get(url_incidents_assignee, headers=ticket_manager.headers, params=params_inc_assignee)

            logger.info(f"Status code incidentes por Assignee Groups: {response_inc_assignee.status_code}")
            if response_inc_assignee.status_code != 200:
                logger.error(f"Error en incidentes por Assignee Groups: {response_inc_assignee.text}")
        else:
            response_inc_assignee = None

        # Función auxiliar para procesar incidentes
        def procesar_incidente(values, fuente="Direct Contact"):
            # FILTRO: Verificar que la cédula coincida con la consultada (solo para búsqueda por cédula)
            if fuente == "Direct Contact":
                cedula_incidente = values.get('Direct Contact Corporate ID', '') or values.get('Corporate ID', '') or values.get('Customer Corporate ID', '')
                if cedula_incidente and cedula_incidente != cedula:
                    return None  # Saltar este incidente si la cédula no coincide

            # CORRECCIÓN FINAL: Description va en resumen, Detailed Decription va en descripción detallada
            description_field = values.get('Description', '').strip()  # Campo corto para resumen
            detailed_decription_field = values.get('Detailed Decription', '') or values.get('Detailed Description', '') or values.get('Notes', '')  # Campo largo para descripción detallada

            # LÓGICA MEJORADA: Si Description está vacío, usar un resumen genérico basado en el tipo de caso
            if description_field:
                # Usar el campo Description como resumen
                descripcion_principal = description_field  # CORRECCIÓN: usar directamente el campo corto
            else:
                # Si Description está vacío, crear un resumen genérico más descriptivo
                incident_id = values.get('Incident Number', 'N/A')
                status = values.get('Status', 'N/A')

                # Usar la descripción completa sin truncar
                if detailed_decription_field:
                    descripcion_principal = detailed_decription_field
                else:
                    descripcion_principal = f"Incidente {incident_id} - Estado: {status}"

            # La descripción detallada siempre usa el campo largo
            description = detailed_decription_field if detailed_decription_field else 'Sin descripción disponible'
            customer = values.get('Customer') or values.get('Customer First Name', '') + ' ' + values.get('Customer Last Name', '') or values.get('Requestor') or 'No especificado'

            # Detailed Description como campo secundario
            descripcion_detallada = detailed_decription_field if detailed_decription_field else 'Sin descripción detallada disponible'

            # Buscar campos relacionados con aplicativos y equipos (información adicional)
            aplicativo_info = []
            for key, value in values.items():
                if value and isinstance(value, str):
                    key_lower = key.lower()
                    if any(term in key_lower for term in ['aplicativo', 'application', 'app', 'dwp', 'equipo', 'equipment', 'device', 'ci', 'service', 'categorization', 'tier']):
                        aplicativo_info.append(f"{key}: {value}")

            # Crear información técnica separada
            info_tecnica = "\\n".join(aplicativo_info) if aplicativo_info else 'Sin información técnica disponible'

            # Intentar obtener fecha de creación de múltiples campos
            fecha_creacion = (values.get('Create Date') or
                            values.get('Submit Date') or
                            values.get('Date/Time Stamp') or
                            values.get('Entry Date'))

            # Intentar extraer nombre del cliente de múltiples fuentes
            nombre_cliente = customer.strip()
            if not nombre_cliente:
                # Buscar en detailed_description
                if 'Nombre completo del afectado:' in descripcion_detallada:
                    import re
                    match = re.search(r'Nombre completo del afectado:\s*([^\n]+)', descripcion_detallada)
                    if match:
                        nombre_cliente = match.group(1).strip()
                # Alternativamente, usar otros campos
                if not nombre_cliente:
                    nombre_cliente = (values.get('Direct Contact First Name', '') + ' ' +
                                    values.get('Direct Contact Last Name', '')).strip()
                if not nombre_cliente:
                    nombre_cliente = values.get('Submitter', '').strip()

            incidente_info = {
                'incident_id': values.get('Incident Number'),
                # 'summary': descripcion_principal,  # ELIMINADO: Campo resumen removido completamente
                'status': values.get('Status'),
                'description': descripcion_principal,  # Campo Description como principal
                'detailed_description': descripcion_detallada,  # Campo Detailed Decription como secundario
                'technical_info': info_tecnica,  # Información técnica separada
                'assigned_to': values.get('Assigned To'),
                'assignee_groups': values.get('Assignee Groups'),
                'last_modified_date': values.get('Last Modified Date'),
                'create_date': fecha_creacion,
                'priority': values.get('Priority'),
                'impact': values.get('Impact'),
                'urgency': values.get('Urgency'),
                'customer': nombre_cliente,
                'login_id': values.get('Requestor ID'),
                'service_type': values.get('Service Type'),
                'service': values.get('Service'),
                'region': values.get('Region'),
                'site_group': values.get('Site Group'),
                'resolution': values.get('Resolution'),
                'resolution_method': values.get('Resolution Method'),
                'dwp_number': values.get('DWP_SRID') or values.get('Incident Number') or 'N/A',
                'found_by': fuente  # Agregar información sobre cómo se encontró
            }
            return incidente_info

        # Agregar incidentes encontrados por cédula a la lista existente
        if response_inc.status_code == 200:
            data_inc = response_inc.json()
            entries_inc = data_inc.get('entries', [])

            for entry in entries_inc:
                values = entry.get('values', {})
                incidente_info = procesar_incidente(values, "Direct Contact")
                if incidente_info:
                    incidentes.append(incidente_info)

        # Agregar incidentes encontrados por Assignee Groups
        if response_inc_assignee and response_inc_assignee.status_code == 200:
            data_inc_assignee = response_inc_assignee.json()
            entries_inc_assignee = data_inc_assignee.get('entries', [])

            logger.info(f"Encontrados {len(entries_inc_assignee)} incidentes por Assignee Groups")

            for entry in entries_inc_assignee:
                values = entry.get('values', {})
                incident_number = values.get('Incident Number')

                # Evitar duplicados - verificar si ya tenemos este incidente
                if not any(inc.get('incident_id') == incident_number for inc in incidentes):
                    incidente_info = procesar_incidente(values, "Assignee Groups")
                    if incidente_info:
                        incidentes.append(incidente_info)
                        logger.info(f"Agregado incidente {incident_number} encontrado por Assignee Groups")
                else:
                    logger.info(f"Incidente {incident_number} ya existe, omitiendo duplicado")
        
        # Obtener notas para cada incidente
        for incidente in incidentes:
            try:
                # Consultar notas del incidente
                incident_id = incidente.get('incident_id')
                if incident_id:
                    # URL para consultar notas de incidentes
                    url_notes = f"{api_base_url}/arsys/v1/entry/HPD:WorkLog"
                    params_notes = {
                        "q": f"'Incident Number'=\"{incident_id}\" AND 'Work Log Type' = \"General Information\"",
                        "limit": "2"  # Las dos notas más recientes
                    }
                    
                    response_notes = requests.get(url_notes, headers=ticket_manager.headers, params=params_notes)
                    
                    if response_notes.status_code == 200:
                        notes_data = response_notes.json()
                        notes_entries = notes_data.get('entries', [])
                        
                        if notes_entries:
                            # Crear lista con las notas encontradas
                            notes_list = []
                            for i, entry in enumerate(notes_entries):
                                note_values = entry.get('values', {})
                                note_info = {
                                    'note_number': i + 1,
                                    'description': note_values.get('Summary', 'Sin resumen'),
                                    'detailed_description': note_values.get('Detailed Description', 'Sin descripción detallada'),
                                    'last_modified_date': note_values.get('Last Modified Date', 'N/A')
                                }
                                notes_list.append(note_info)
                            
                            # Solo mantener last_note, eliminar notes
                            incidente['last_note'] = notes_list[0] if notes_list else None
                        else:
                            incidente['last_note'] = {
                                'description': 'Sin notas',
                                'detailed_description': 'No hay notas disponibles para este incidente'
                            }
                    else:
                        incidente['last_note'] = {
                            'description': 'Error al obtener notas',
                            'detailed_description': f'Error {response_notes.status_code}: {response_notes.text}'
                        }
                else:
                    incidente['last_note'] = {
                        'description': 'Sin ID de incidente',
                        'detailed_description': 'No se puede consultar notas sin ID de incidente'
                    }
            except Exception as e:
                incidente['last_note'] = {
                    'description': 'Error en consulta',
                    'detailed_description': f'Error al consultar notas: {str(e)}'
                }
        
        print(f"Se encontraron {len(incidentes)} incidentes")

        # APLICAR FILTROS DE CÉDULA ANTES DE GUARDAR EL JSON
        if user_info and user_info.get('found') and user_info.get('cedula'):
            cedula_filtro = user_info.get('cedula')
            login_id_filtro = user_info.get('user_data', {}).get('Remedy Login ID', '') if user_info.get('user_data') else ''

            print(f"Aplicando filtros: cédula={cedula_filtro}, login_id={login_id_filtro}")

            # Filtrar tickets por cédula
            tickets_filtrados = []
            for ticket in tickets:
                cedula_ticket = ticket.get('customer_person_id', '') or ticket.get('person_id', '') or ticket.get('requestor_id', '')
                if not cedula_ticket or cedula_ticket == cedula_filtro:
                    tickets_filtrados.append(ticket)

            # Filtrar incidentes por cédula - INCLUIR TODOS LOS CASOS QUE PERTENECEN AL USUARIO
            incidentes_filtrados = []
            for incidente in incidentes:
                incluir_incidente = False

                # summary = incidente.get('summary', '')  # ELIMINADO: Campo resumen removido completamente
                detailed_desc = incidente.get('detailed_description', '')
                corporate_id = incidente.get('corporate_id', '')
                assignee_groups = incidente.get('assignee_groups', '')

                # CRITERIO 1: Verificar si la cédula aparece en el campo "Usuario con el que ingresó"
                if f"Usuario con el que ingresó: {cedula_filtro}" in detailed_desc:
                    incluir_incidente = True
                    print(f"  ✓ Incidente {incidente.get('incident_id')} incluido por cédula en 'Usuario con el que ingresó'")

                # CRITERIO 2: Verificar si el Corporate ID del incidente coincide con la cédula del usuario
                elif corporate_id == cedula_filtro:
                    incluir_incidente = True
                    print(f"  ✓ Incidente {incidente.get('incident_id')} incluido por Corporate ID coincidente")

                # CRITERIO 3: Verificar si el usuario está en los Assignee Groups
                elif login_id_filtro and login_id_filtro in assignee_groups:
                    incluir_incidente = True
                    print(f"  ✓ Incidente {incidente.get('incident_id')} incluido por login en Assignee Groups")

                # CRITERIO 4: Verificar si es un caso donde el usuario reportó directamente (buscar su nombre completo)
                elif user_info.get('first_name') and user_info.get('last_name'):
                    nombre_completo = f"{user_info.get('first_name')} {user_info.get('last_name')}"
                    # Buscar el nombre completo como "Nombre completo del afectado"
                    if f"Nombre completo del afectado: {nombre_completo.upper()}" in detailed_desc.upper():
                        incluir_incidente = True
                        print(f"  ✓ Incidente {incidente.get('incident_id')} incluido por nombre del afectado")

                # CRITERIO 5: Verificar si aparece en las notas como quien levantó el incidente
                elif "Lizeth quien levanto el incidente" in detailed_desc:
                    incluir_incidente = True
                    print(f"  ✓ Incidente {incidente.get('incident_id')} incluido por referencia en notas")

                if not incluir_incidente:
                    print(f"  ✗ Incidente {incidente.get('incident_id')} EXCLUIDO - no cumple criterios de filtrado")

                if incluir_incidente:
                    incidentes_filtrados.append(incidente)

            # Actualizar las listas con los datos filtrados
            tickets_originales = len(tickets)
            incidentes_originales = len(incidentes)
            tickets = tickets_filtrados
            incidentes = incidentes_filtrados

            print(f"Filtrado aplicado: tickets {tickets_originales} -> {len(tickets)}, incidentes {incidentes_originales} -> {len(incidentes)}")

        if not tickets and not incidentes:
            print(f"\n{Fore.YELLOW}No se encontraron tickets ni incidentes para la cédula {cedula}{Style.RESET_ALL}")
            return None

        # Obtener fecha y hora actuales
        fecha_consulta = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Obtener información del usuario para incluir en el reporte
        user_info_for_report = buscar_usuario_por_cedula(ticket_manager, cedula, logger)

        # Crear estructura para el reporte unificado
        reporte = {
            "user_info": {
                "cedula": cedula,
                "found": user_info_for_report['found'] if user_info_for_report else False,
                "first_name": user_info_for_report.get('first_name', '') if user_info_for_report and user_info_for_report['found'] else '',
                "last_name": user_info_for_report.get('last_name', '') if user_info_for_report and user_info_for_report['found'] else '',
                "full_name": user_info_for_report.get('full_name', '') if user_info_for_report and user_info_for_report['found'] else '',
                "email": user_info_for_report.get('user_data', {}).get('Internet E-mail', '') if user_info_for_report and user_info_for_report['found'] else '',
                "company": user_info_for_report.get('user_data', {}).get('Company', '') if user_info_for_report and user_info_for_report['found'] else '',
                "organization": user_info_for_report.get('user_data', {}).get('Organization', '') if user_info_for_report and user_info_for_report['found'] else ''
            },
            "tickets": {
                "cedula": cedula,
                "fecha_consulta": fecha_consulta,
                "total_tickets": len(tickets),
                "tickets_procesados": len(tickets),
                "tickets": tickets
            },
            "incidents": {
                "login_id": cedula,
                "fecha_consulta": fecha_consulta,
                "total_incidentes": len(incidentes),
                "incidentes_procesados": len(incidentes),
                "incidentes": incidentes
            }
        }
        
        # Guardar el reporte como JSON
        reporte_file = os.path.join(os.path.dirname(__file__), "reporte_unificado.json")
        with open(reporte_file, 'w', encoding='utf-8') as f:
            json.dump(reporte, f, indent=2, ensure_ascii=False)
        
        print(f"\n{Fore.GREEN}Reporte unificado generado correctamente: {reporte_file}{Style.RESET_ALL}")
        
        return reporte_file
    except Exception as e:
        print(f"\n{Fore.RED}Error al buscar tickets: {str(e)}{Style.RESET_ALL}")
        return None


def _display_tickets_section(data: Dict[str, Any], displayer, logger):
    """Display tickets section of the report."""
    if "tickets" not in data or not isinstance(data["tickets"], dict):
        return

    tickets_info = data["tickets"]
    cedula = tickets_info.get("cedula", "N/A")
    fecha_consulta = tickets_info.get("fecha_consulta", "N/A")
    total_tickets = tickets_info.get("total_tickets", 0)
    tickets_procesados = tickets_info.get("tickets_procesados", 0)

    displayer.display_section_header("INFORMACIÓN DE TICKETS")
    displayer.display_info_line("Cédula", cedula)
    displayer.display_info_line("Fecha de consulta", fecha_consulta)
    displayer.display_info_line("Total de tickets", total_tickets)
    displayer.display_info_line("Tickets procesados", tickets_procesados)

    # Mostrar tickets
    tickets = tickets_info.get("tickets", [])
    if tickets:
        displayer.display_section_header(f"TICKETS ({len(tickets)})")

        priority_fields = ["request_id", "summary", "status", "priority", "dwp_number", "detailed_description"]

        for i, ticket in enumerate(tickets):
            request_id = ticket.get('request_id', 'Sin ID')
            displayer.display_item_header("Ticket", i+1, request_id)

            # Create and display table
            table = displayer.create_item_table(ticket, priority_fields)
            displayer.display_table(table)

            # Display last note if exists
            if 'last_note' in ticket and ticket['last_note']:
                displayer.display_note_header()
                note_table = displayer.create_note_table(ticket['last_note'])
                displayer.display_table(note_table)
    else:
        displayer.display_warning("No se encontraron tickets.")


def _display_incidents_section(data: Dict[str, Any], displayer, logger, mostrar_todos: bool = False):
    """Display incidents section of the report."""
    if "incidents" not in data or not isinstance(data["incidents"], dict):
        return

    incidents_info = data["incidents"]
    login_id = incidents_info.get("login_id", "N/A")
    fecha_consulta = incidents_info.get("fecha_consulta", "N/A")
    total_incidentes = incidents_info.get("total_incidentes", 0)
    incidentes_procesados = incidents_info.get("incidentes_procesados", 0)

    displayer.display_section_header("INFORMACIÓN DE INCIDENTES")
    displayer.display_info_line("Login ID", login_id)
    displayer.display_info_line("Fecha de consulta", fecha_consulta)
    displayer.display_info_line("Total de incidentes", total_incidentes)
    displayer.display_info_line("Incidentes procesados", incidentes_procesados)

    # Mostrar incidentes
    incidentes = incidents_info.get("incidentes", [])
    if incidentes:
        displayer.display_section_header(f"INCIDENTES ({len(incidentes)})")

        # Determinar cuántos incidentes mostrar
        if mostrar_todos:
            max_incidentes = len(incidentes)
        elif limite:
            max_incidentes = min(limite, len(incidentes))
        else:
            max_incidentes = min(5, len(incidentes))  # Default: 5 incidentes

        priority_fields = ["incident_id", "summary", "status", "priority", "dwp_number", "detailed_description"]

        for i, incidente in enumerate(incidentes[:max_incidentes]):
            incident_id = incidente.get('incident_id', 'Sin ID')
            displayer.display_item_header("Incidente", i+1, incident_id)

            # Create and display table
            table = displayer.create_item_table(incidente, priority_fields)
            displayer.display_table(table)

            # Display last note if exists
            if 'last_note' in incidente and incidente['last_note']:
                displayer.display_note_header()
                note_table = displayer.create_note_table(incidente['last_note'])
                displayer.display_table(note_table)

        # Show message if not all incidents are displayed
        if not mostrar_todos and len(incidentes) > max_incidentes:
            if limite:
                displayer.display_warning(f"Mostrando {max_incidentes} de {len(incidentes)} incidentes (límite: {limite}). Use '--todos' para ver todos.")
            else:
                displayer.display_warning(f"Mostrando {max_incidentes} de {len(incidentes)} incidentes. Use '--todos' para ver todos o '--limite N' para especificar cantidad.")
    else:
        displayer.display_warning("No se encontraron incidentes.")


if __name__ == "__main__":
    # Verificar argumentos
    if len(sys.argv) > 1:
        if sys.argv[1] == '--simple':
            # Modo simple: solo procesar JSON existente para enviar por correo
            json_file = os.path.join(os.path.dirname(__file__), "test_report.json")
            from test_mail import crear_mensaje_desde_json
            mensaje_html = crear_mensaje_desde_json(json_file)
            print("Mensaje HTML generado correctamente para envío por correo.")
        else:
            # Buscar tickets por cédula
            cedula = sys.argv[1]
            mostrar_todos = '--todos' in sys.argv

            # Verificar si se especificó un límite
            limite = None
            if '--limite' in sys.argv:
                try:
                    limite_index = sys.argv.index('--limite')
                    if limite_index + 1 < len(sys.argv):
                        limite = int(sys.argv[limite_index + 1])
                except (ValueError, IndexError):
                    print("Error: El parámetro --limite debe ser seguido por un número válido")
                    sys.exit(1)

            # Ejecutar búsqueda y generar el reporte
            reporte_file = buscar_tickets_por_cedula(cedula, mostrar_todos)

            if reporte_file:
                # Mostrar el reporte en la consola - CORRECCIÓN: pasar mostrar_todos y limite
                mostrar_reporte_unificado(reporte_file, mostrar_todos, limite)
    else:
        print(f"\n{Fore.YELLOW}Uso: python {sys.argv[0]} <cedula> [--todos]{Style.RESET_ALL}")
        print(f"  --todos: Muestra todos los incidentes (por defecto solo muestra los 5 primeros)")
        print(f"  --simple: Procesa un JSON existente para enviar por correo")
